<!doctype html>
<html class="h-full bg-white">
<head><title>Autobooks</title>
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<!--<link rel="stylesheet" href="css/style.css">-->
<link rel="shortcut icon" href="{{ APP_ROOT }}/system/img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<!-- Jodit Editor -->
<link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
<script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>

<script>
    // Define APP_ROOT for JavaScript files
    var APP_ROOT = '<?= APP_ROOT ?>';

    // Navigation tree initialization function
    function initNavTree() {
        return {
            currentRoute: localStorage.getItem('currentNavRoute') || window.location.pathname.replace(APP_ROOT, '').replace(/^\/+|\/+$/g, ''),
            sortableInstances: [],
            init() {
                // Set initial route based on current URL
                if (!localStorage.getItem('currentNavRoute')) {
                    localStorage.setItem('currentNavRoute', this.currentRoute);
                }

                // Initialize sortable functionality after a short delay
                setTimeout(() => this.initSortable(), 100);

                // Listen for HTMX after-request events
                document.body.addEventListener('htmx:afterOnLoad', (event) => {
                    // Don't update if this is a background request (not navigation)
                    if (event.detail.target.id !== 'content_wrapper') return;

                    // Extract the path from the URL
                    const url = new URL(event.detail.xhr.responseURL);
                    const path = url.pathname.replace(APP_ROOT, '');
                    const cleanPath = path.replace(/^\/+|\/+$/g, '');

                    // Update the current route
                    this.currentRoute = cleanPath;
                    localStorage.setItem('currentNavRoute', cleanPath);

                    // Dispatch a custom event to synchronize all nav-tree instances
                    window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: cleanPath } }));
                });

                // Listen for route change events from other nav-tree instances
                window.addEventListener('nav-route-changed', (event) => {
                    this.currentRoute = event.detail.route;
                });

                // Reinitialize sortable after HTMX updates
                document.body.addEventListener('htmx:afterSettle', () => {
                    this.destroySortable();
                    setTimeout(() => this.initSortable(), 100);
                });
            },
            destroySortable() {
                // Destroy existing sortable instances
                this.sortableInstances.forEach(instance => {
                    if (instance && instance.destroy) {
                        instance.destroy();
                    }
                });
                this.sortableInstances = [];
            },
            initSortable() {
                // Only initialize if user has admin/dev role
                if (!document.querySelector('.drag-handle')) return;

                // Find all sortable lists
                const sortableLists = document.querySelectorAll('[data-sortable="true"]');

                sortableLists.forEach(list => {
                    const parentPath = list.dataset.parentPath || 'root';
                    const depth = parseInt(list.dataset.depth || '0');

                    const sortable = Sortable.create(list, {
                        group: {
                            name: 'nav-tree',
                            pull: true,
                            put: true
                        },
                        animation: 150,
                        handle: '.drag-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        fallbackOnBody: true,
                        swapThreshold: 0.65,
                        onStart: (evt) => {
                            // Add visual feedback
                            document.body.classList.add('sorting-active');
                            evt.item.classList.add('dragging');
                        },
                        onEnd: (evt) => {
                            // Remove visual feedback
                            document.body.classList.remove('sorting-active');
                            evt.item.classList.remove('dragging');

                            // Handle the drop
                            this.handleSortableEnd(evt, parentPath);
                        },
                        onMove: (evt) => {
                            // Prevent dropping on non-sortable items
                            return evt.related.classList.contains('sortable-item');
                        }
                    });

                    this.sortableInstances.push(sortable);
                });
            },
            handleSortableEnd(evt, originalParentPath) {
                const item = evt.item;
                const newIndex = evt.newIndex;
                const oldIndex = evt.oldIndex;
                const from = evt.from;
                const to = evt.to;

                // Get the actual parent paths from the containers
                const fromParentPath = from.dataset.parentPath || 'root';
                const toParentPath = to.dataset.parentPath || 'root';

                // Check if item was moved to a different list (subfolder creation)
                if (from !== to) {
                    const itemKey = item.dataset.routeKey;

                    // Move to different parent (subfolder)
                    htmx.ajax('POST', APP_ROOT + '/api/system/nav_tree/move_to_subfolder', {
                        values: {
                            item_key: itemKey,
                            target_key: toParentPath.split('/').pop(), // Get the last part of the path
                            current_parent: fromParentPath,
                            target_parent: toParentPath
                        },
                        target: 'body',
                        swap: 'none'
                    }).then(() => {
                        // Refresh the navigation tree after successful move
                        setTimeout(() => window.location.reload(), 500);
                    }).catch(() => {
                        // Revert the move on error
                        from.insertBefore(item, from.children[oldIndex]);
                    });
                } else if (oldIndex !== newIndex) {
                    // Reorder within same parent
                    const items = Array.from(to.querySelectorAll('.sortable-item')).map((el, index) => ({
                        route_key: el.dataset.routeKey,
                        sort_order: index + 1
                    }));

                    htmx.ajax('POST', APP_ROOT + '/api/system/nav_tree/reorder_navigation', {
                        values: {
                            items: JSON.stringify(items),
                            parent_path: toParentPath
                        },
                        target: 'body',
                        swap: 'none'
                    }).catch(() => {
                        // Revert the move on error
                        if (oldIndex < newIndex) {
                            to.insertBefore(item, to.children[oldIndex]);
                        } else {
                            to.insertBefore(item, to.children[oldIndex + 1]);
                        }
                    });
                }
            }
        };
    }
</script>

<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<script defer src="https://unpkg.com/htmx.org@2.0.3"></script>
<script defer src="https://unpkg.com/htmx-ext-class-tools@2.0.1/class-tools.js"></script>
<script defer src="resources/components/js/notification-handler.js"></script>
<script defer src="<?= APP_ROOT ?>/resources/js/htmx-sse.js"></script>

<SCRIPT>
                        'select:not([disabled])',
                        'textarea:not([disabled])',
                    ]
                        .map((selector) => `${selector}:not([tabindex='-1'])`)
                        .join(',')

                    this.open = false

                    if (!$event.target.closest(focusableSelector)) {
                        this.focusButton()
                    }
                }
            },

            onMouseEnter(evt) {
                pointer.update(evt)
            },
            onMouseMove(evt, newIndex) {
                // Only highlight when the cursor has moved
                // Pressing arrow keys can otherwise scroll the container and override the selected item
                if (!pointer.wasMoved(evt)) return
                this.activeIndex = newIndex
            },
            onMouseLeave(evt) {
                // Only unhighlight when the cursor has moved
                // Pressing arrow keys can otherwise scroll the container and override the selected item
                if (!pointer.wasMoved(evt)) return
                this.activeIndex = -1
            },
        }
    }

    window.Components.popoverGroup = function popoverGroup() {
        return {
            __type: 'popoverGroup',
            init() {
                let handler = (e) => {
                    if (!document.body.contains(this.$el)) {
                        window.removeEventListener('focus', handler, true)
                        return
                    }
                    if (e.target instanceof Element && !this.$el.contains(e.target)) {
                        window.dispatchEvent(
                            new CustomEvent('close-popover-group', {
                                detail: this.$el,
                            })
                        )
                    }
                }
                window.addEventListener('focus', handler, true)
            },
        }
    }

    window.Components.popover = function popover({ open = false, focus = false } = {}) {
        const focusableSelector = [
            '[contentEditable=true]',
            '[tabindex]',
            'a[href]',
            'area[href]',
            'button:not([disabled])',
            'iframe',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
        ]
            .map((selector) => `${selector}:not([tabindex='-1'])`)
            .join(',')

        function focusFirst(container) {
            const focusableElements = Array.from(container.querySelectorAll(focusableSelector))

            function tryFocus(element) {
                if (element === undefined) return

                element.focus({ preventScroll: true })

                if (document.activeElement !== element) {
                    tryFocus(focusableElements[focusableElements.indexOf(element) + 1])
                }
            }

            tryFocus(focusableElements[0])
        }

        return {
            __type: 'popover',
            open,
            init() {
                if (focus) {
                    this.$watch('open', (open) => {
                        if (open) {
                            this.$nextTick(() => {
                                focusFirst(this.$refs.panel)
                            })
                        }
                    })
                }

                let handler = (e) => {
                    if (!document.body.contains(this.$el)) {
                        window.removeEventListener('focus', handler, true)
                        return
                    }
                    let ref = focus ? this.$refs.panel : this.$el
                    if (this.open && e.target instanceof Element && !ref.contains(e.target)) {
                        let node = this.$el
                        while (node.parentNode) {
                            node = node.parentNode
                            if (node.__x instanceof this.constructor) {
                                if (node.__x.$data.__type === 'popoverGroup') return
                                if (node.__x.$data.__type === 'popover') break
                            }
                        }
                        this.open = false
                    }
                }

                window.addEventListener('focus', handler, true)
            },
            onEscape() {
                this.open = false
                if (this.restoreEl) {
                    this.restoreEl.focus()
                }
            },
            onClosePopoverGroup(e) {
                if (e.detail.contains(this.$el)) {
                    this.open = false
                }
            },
            toggle(e) {
                this.open = !this.open
                if (this.open) {
                    this.restoreEl = e.currentTarget
                } else if (this.restoreEl) {
                    this.restoreEl.focus()
                }
            },
        }
    }

    window.Components.radioGroup = function radioGroup({ initialCheckedIndex = 0 } = {}) {
        return {
            value: undefined,
            active: undefined,
            init() {
                let options = Array.from(this.$el.querySelectorAll('input'))

                this.value = options[initialCheckedIndex]?.value

                for (let option of options) {
                    option.addEventListener('change', () => {
                        this.active = option.value
                    })
                    option.addEventListener('focus', () => {
                        this.active = option.value
                    })
                }

                window.addEventListener(
                    'focus',
                    () => {
                        if (!options.includes(document.activeElement)) {
                            this.active = undefined
                        }
                    },
                    true
                )
            },
        }
    }

    window.Components.tabs = function tabs() {
        return {
            selectedIndex: 0,
            onTabClick(event) {
                if (!this.$el.contains(event.detail)) return

                let tabs = Array.from(this.$el.querySelectorAll('[x-data^="Components.tab("]'))
                let panels = Array.from(this.$el.querySelectorAll('[x-data^="Components.tabPanel("]'))

                let idx = tabs.indexOf(event.detail)
                this.selectedIndex = idx

                window.dispatchEvent(
                    new CustomEvent('tab-select', {
                        detail: {
                            tab: event.detail,
                            panel: panels[idx],
                        },
                    })
                )
            },
            onTabKeydown(event) {
                if (!this.$el.contains(event.detail.tab)) return

                let tabs = Array.from(this.$el.querySelectorAll('[x-data^="Components.tab("]'))
                let tabIndex = tabs.indexOf(event.detail.tab)

                if (event.detail.key === 'ArrowLeft') {
                    this.onTabClick({ detail: tabs[(tabIndex - 1 + tabs.length) % tabs.length] })
                } else if (event.detail.key === 'ArrowRight') {
                    this.onTabClick({ detail: tabs[(tabIndex + 1) % tabs.length] })
                } else if (event.detail.key === 'Home' || event.detail.key === 'PageUp') {
                    this.onTabClick({ detail: tabs[0] })
                } else if (event.detail.key === 'End' || event.detail.key === 'PageDown') {
                    this.onTabClick({ detail: tabs[tabs.length - 1] })
                }
            },
        }
    }

    window.Components.tab = function tab(defaultIndex = 0) {
        return {
            selected: false,
            init() {
                let tabs = Array.from(
                    this.$el
                        .closest('[x-data^="Components.tabs("]')
                        .querySelectorAll('[x-data^="Components.tab("]')
                )
                this.selected = tabs.indexOf(this.$el) === defaultIndex
                this.$watch('selected', (selected) => {
                    if (selected) {
                        this.$el.focus()
                    }
                })
            },
            onClick() {
                window.dispatchEvent(
                    new CustomEvent('tab-click', {
                        detail: this.$el,
                    })
                )
            },
            onKeydown(event) {
                if (['ArrowLeft', 'ArrowRight', 'Home', 'PageUp', 'End', 'PageDown'].includes(event.key)) {
                    event.preventDefault()
                }

                window.dispatchEvent(
                    new CustomEvent('tab-keydown', {
                        detail: {
                            tab: this.$el,
                            key: event.key,
                        },
                    })
                )
            },
            onTabSelect(event) {
                this.selected = event.detail.tab === this.$el
            },
        }
    }

    window.Components.tabPanel = function tabPanel(defaultIndex = 0) {
        return {
            selected: false,
            init() {
                let panels = Array.from(
                    this.$el
                        .closest('[x-data^="Components.tabs("]')
                        .querySelectorAll('[x-data^="Components.tabPanel("]')
                )
                this.selected = panels.indexOf(this.$el) === defaultIndex
            },
            onTabSelect(event) {
                this.selected = event.detail.panel === this.$el
            },
        }
    }

    function useTrackedPointer() {
        /** @type {[x: number, y: number]} */
        let lastPos = [-1, -1]

        return {
            /**
             * @param {PointerEvent} evt
             */
            wasMoved(evt) {
                let newPos = [evt.screenX, evt.screenY]

                if (lastPos[0] === newPos[0] && lastPos[1] === newPos[1]) {
                    return false
                }

                lastPos = newPos
                return true
            },

            /**
             * @param {PointerEvent} evt
             */
            update(evt) {
                lastPos = [evt.screenX, evt.screenY]
            },
        }
    }
</SCRIPT>

<style>
    .htmx-settling-in {
        background-color: yellow;
        transition: background-color 1s ease-in-out;
    }
    .htmx-settling-out {
        background-color: white;
        transition: background-color 1s ease-in-out;
    }

    /* Sortable styles */
    .sortable-ghost {
        opacity: 0.4;
        background: rgba(59, 130, 246, 0.1) !important;
    }

    .sortable-chosen {
        background: rgba(59, 130, 246, 0.2) !important;
    }

    .sortable-drag {
        transform: rotate(5deg);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .drop-target {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 2px dashed #22c55e !important;
    }

    .sorting-active .drag-handle {
        cursor: grabbing !important;
    }

    .drag-handle:hover {
        opacity: 0.8;
        transform: scale(1.1);
        transition: all 0.2s ease;
    }

    .dragging {
        opacity: 0.6;
        transform: rotate(2deg);
    }

    /* Ensure proper z-index for dragged items */
    .sortable-drag {
        z-index: 9999 !important;
    }

    /* Style for nested lists to maintain depth appearance */
    [data-depth="1"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.3);
    }

    [data-depth="2"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.5);
    }

    [data-depth="3"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.7);
    }
</style>
<style>
    html {
        font-family: InterVariable, sans-serif !important;
    }
</style>
    <!-- Include Jodit CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
    <script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>
    </head>
